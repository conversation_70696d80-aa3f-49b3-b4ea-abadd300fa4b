import logging
import os
import uuid
from io import BytesIO
from typing import Optional, Union, Tuple, Dict, List

import geopandas as gpd
import matplotlib
import pandas as pd
import sweetviz
from django.conf import settings
from django.core.files.storage import default_storage
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from pandas_profiling import ProfileReport
from pandas_profiling.config import Settings

from common.handlers.kml import KMLHandler
from common.utils.general import slice_list_to_chunks
from layers.models import Layer
from workspaces.models.eda_report import EDAReportSourceChoices

logger = logging.getLogger("common")

# Set a non-GUI backend for Matplotlib
matplotlib.use("Agg")

MEDIA_ROOT = getattr(settings, "MEDIA_ROOT") or "media"
EDA_REPORT_DIR = os.path.join(MEDIA_ROOT, "reports", "eda_reports")

LOAD_DATA_MAPPER = {
    "csv": lambda file_object: pd.read_csv(file_object),
    "geojson": lambda file_object: gpd.read_file(file_object),
    "gpkg": lambda file_object: gpd.read_file(file_object),
    "shp": lambda file_object: gpd.read_file(file_object),
    "kml": lambda file_object: KMLHandler(file_object).load_kml(),
}


class StorageBackend:
    """
    A generic storage backend interface to handle file operations and metadata.
    """

    @staticmethod
    def save(file_path: str, content: BytesIO):
        """
        Save a file to storage.
        :param file_path: Path to save the file.
        :param content: File content in BytesIO format.
        """
        default_storage.save(name=file_path, content=content)

    @staticmethod
    def open(file_path: str):
        """
        Open a file from local storage.
        """
        paths = file_path.split(settings.MEDIA_URL)
        file_path = paths[1] if len(paths) > 1 else paths[0]
        return default_storage.open(file_path, "rb")

    @staticmethod
    def validate_file_path_is_exists(file_path: str):
        paths = file_path.split(settings.MEDIA_URL)
        file_path = paths[1] if len(paths) > 1 else paths[0]
        if not default_storage.exists(file_path):
            raise ValueError(
                _("File does not exist! %(file_path)s") % {"file_path": file_path}
            )

    @staticmethod
    def delete(file_path: str):
        default_storage.delete(file_path)


class EDAReportGenerator:
    """
    A utility for generating EDA reports from various data file formats and storing them.
    """

    def __init__(self, identifier: str, reports_location: str = EDA_REPORT_DIR):
        self.reports_location = reports_location
        self.storage_backend = StorageBackend()
        self.identifier = identifier
        self._columns = None
        self._logger = logging.getLogger(__name__)
        self.debug = lambda method, message: self._logger.debug(
            f"[{self.__class__}][{method}] {message}"
        )

    def generate_eda_reports_from_file(
        self, file: Union[str, BytesIO], title: str, file_type: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """
        Generate an EDA report and save it to storage.

        :param file: File path, file URL, or file-like object containing the dataset.
        :param title: Title for the EDA report.
        :param file_type: Optional file type to override automatic file type detection.
        :return: Path to the generated EDA report.
        """
        # validate the data file
        self.storage_backend.validate_file_path_is_exists(file_path=file)

        # Load data
        df = self._load_data(file, file_type)
        self.columns = df.columns.tolist()

        # Convert GeoDataFrame to DataFrame if needed
        if isinstance(df, gpd.GeoDataFrame):
            df = pd.DataFrame(df.drop(columns="geometry"))

        # Generate EDA HTML report
        eda_reports = self._generate_eda_reports(df, title)
        # Save the report to storage and return the report path
        return eda_reports

    def generate_eda_reports_from_layer(self, layer: Layer, title: str):
        """
        Generate an EDA report from the records of a specific layer in the database.

        :param layer: Layer instance or layer ID.
        :param title: Title for the EDA report.
        :return: Tuple containing hash code and path to the generated EDA report.
        """
        # Fetch all records for the layer
        records_source = layer.records.only("data", "source_properties")
        if not records_source:
            return None

        records_data = []
        for records in slice_list_to_chunks(
            records_source, chunk_size=settings.RECORDS_BATCH_SIZE
        ):
            records_data.extend(
                record.data if record.data else record.source_properties
                for record in records
            )

        # Convert to DataFrame
        df = pd.DataFrame(records_data)
        if df.empty:
            return None

        self.columns = df.columns.tolist()
        eda_reports = self._generate_eda_reports(df, title)
        return eda_reports

    def _generate_eda_reports(
        self, df: pd.DataFrame, title: str
    ) -> List[Dict[str, str]]:
        eda_reports = list()
        for eda_report_source in EDAReportSourceChoices.values:
            try:
                hash_code, hash_path = self._generate_and_save_eda_report(
                    df, title, eda_report_source
                )
            except Exception as e:
                self.debug(
                    "_generate_eda_reports", f"Failed to generate EDA report: {e}"
                )
                continue
            eda_reports.append(
                {
                    "hash": hash_code,
                    "report_path": hash_path,
                    "source": eda_report_source,
                    "created_at": timezone.now().isoformat(),
                }
            )
        return eda_reports

    def _load_data(
        self, file: Union[str, BytesIO], file_type: Optional[str] = None
    ) -> pd.DataFrame:
        """
        Load data based on a file-like object or path and inferred file type.

        :param file: The file path or file-like object.
        :param file_type: Optional file type for format specification.
        :return: Loaded data as a DataFrame.
        """
        if isinstance(file, str):
            # Handle string paths or URLs through the storage backend
            file = self.storage_backend.open(file)

        file_extension = self._get_file_extension(file, file_type)
        self._check_valid_file_type(file_extension)
        return LOAD_DATA_MAPPER[file_extension](file)

    @staticmethod
    def _check_valid_file_type(file_extension: str):
        if file_extension not in ["csv", "geojson", "gpkg", "shp", "kml"]:
            raise ValueError(f"Unsupported file format: {file_extension}")

    @staticmethod
    def _get_file_extension(file_obj, file_type: Optional[str]) -> str:
        """
        Determine the file extension based on the provided file object and type.
        """
        file_name = getattr(file_obj, "name", None) or "data." + (file_type or "csv")
        return file_type or file_name.split(".")[-1].lower()

    def _generate_and_save_eda_report(
        self, df: pd.DataFrame, title: str, source: EDAReportSourceChoices
    ) -> Tuple[str, str]:
        # Generate unique hash path
        hash_code, hash_path = self._build_hash_path()
        self.debug("_generate_and_save_eda_report", f"source: {source}")
        self.debug(
            "_generate_and_save_eda_report",
            f"hash_code: {hash_code} , hash_path: {hash_path}",
        )
        # Save HTML report to storage
        if source == EDAReportSourceChoices.YDATA:
            profile = ProfileReport(
                df, title=f"{title} EDA Report", config=self._pandas_profiling_config()
            )
            with BytesIO(profile.to_html().encode()) as content_file:
                self.storage_backend.save(hash_path, content_file)
                self.debug(
                    "_generate_and_save_eda_report",
                    f"YData EDA Report saved to {hash_path}",
                )
        elif source == EDAReportSourceChoices.SWEETVIZ:
            eda_report = sweetviz.analyze(df)
            eda_report.show_html(filepath=f"{hash_code}.html", open_browser=False)
            saved_file = open(f"{hash_code}.html", "rb")
            self.storage_backend.save(hash_path, saved_file)
            saved_file.close()
            self.debug(
                "_generate_and_save_eda_report",
                f"Sweetviz EDA Report saved to {hash_path}",
            )
        return hash_code, hash_path

    def _build_hash_path(self) -> Tuple[str, str]:
        """
        Generates a unique hash path for storing the EDA report.
        """
        hash_code = f"{self.identifier}-{str(uuid.uuid4())}"
        report_path = os.path.join(
            self.reports_location, str(self.identifier), f"{hash_code}.html"
        )
        return hash_code, report_path

    @staticmethod
    def _pandas_profiling_config() -> Settings:
        """
        Configures settings for pandas profiling to optimize HTML output.
        """
        config = Settings()
        config.html.inline = True
        config.html.minify_html = True
        config.html.use_local_assets = True
        config.progress_bar = False
        return config

    @property
    def columns(self):
        return self._columns

    @columns.setter
    def columns(self, columns):
        self._columns = columns
