import os

from django.conf import settings

ALLOWED_DATASET_EXTENSIONS = ["csv", "gpkg", "geojson", "kml", "shp"]


def dataset_extension_validator(file_path):
    extension = file_path.split(".")[-1]
    if extension not in ALLOWED_DATASET_EXTENSIONS:
        raise ValueError(
            f"Unsupported file format: {extension}. Allowed extensions are: {ALLOWED_DATASET_EXTENSIONS}"
        )


def dataset_path(instance, filename):
    return os.path.join("media", "original_datasets", filename)


def get_owner_user(organization):
    """
    Retrieves the owner user of the organization.
    """
    return organization.acl_individuals.filter(
        rule__role__codename=settings.MAIN_OWNER_ROLE_CODENAME
    ).first()
