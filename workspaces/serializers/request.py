from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from gabbro.layers_engine.validators import hex_color_validator
from rest_framework import serializers

from workspaces.enums import LongLatEnum, CoordinateTypesEnum
from workspaces.models import WorkspaceRequest


class LayerDataSerializer(serializers.Serializer):
    color = serializers.CharField(max_length=7, validators=[hex_color_validator])
    title = serializers.CharField(max_length=200)
    description = serializers.CharField(default="", allow_blank=True)
    read_only = serializers.BooleanField(default=False)
    created = serializers.CharField(
        default=lambda: timezone.now().strftime("%Y-%m-%dT%H:%M:%S"),
        required=False,
    )
    workspace_id = serializers.IntegerField(required=False, allow_null=True)


class WorkspaceRequestSerializer(serializers.ModelSerializer):
    layer_data = LayerDataSerializer()

    class Meta:
        model = WorkspaceRequest
        fields = [
            "id",
            "created_by",
            "dataset",
            "organization",
            "request_type",
            "layer_data",
        ]


class LocationFieldMappingSerializer(serializers.Serializer):
    coordinate_type = serializers.ChoiceField(
        choices=[(tag.name, tag.value) for tag in CoordinateTypesEnum]
    )
    lat_lon_column_num = serializers.ChoiceField(
        choices=[(tag.name, tag.value) for tag in LongLatEnum]
    )
    longitude_column = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    latitude_column = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )
    lang_lat_column = serializers.CharField(
        required=False, allow_blank=True, allow_null=True
    )

    def validate(self, attrs):
        coordinate_type = attrs["coordinate_type"]
        lat_lon_column_num = attrs["lat_lon_column_num"]
        lang_lat_column = attrs.get("lang_lat_column")
        latitude_column = attrs.get("latitude_column")
        longitude_column = attrs.get("longitude_column")

        if (
            coordinate_type == CoordinateTypesEnum.other.value
            and lat_lon_column_num == LongLatEnum.two_column.value
        ):
            raise serializers.ValidationError(
                {
                    "lat_lon_column_num": _(
                        "lat_lon_column_num cannot be 'two_column' when coordinate_type is 'other'"
                    )
                    % {}
                }
            )

        if lat_lon_column_num == LongLatEnum.column.value and not lang_lat_column:
            raise serializers.ValidationError(
                {"lang_lat_column": _("This field is required.") % {}}
            )
        if lat_lon_column_num == LongLatEnum.two_column.value and not all(
            [longitude_column, latitude_column]
        ):
            raise serializers.ValidationError(
                {
                    "longitude_column": _("This field is required.") % {},
                    "latitude_column": _("This field is required.") % {},
                }
            )
        return attrs


class JSONSchemaSerializer(serializers.Serializer):
    json_schema = serializers.JSONField()
    web_ui_json_schema = serializers.JSONField(required=False, allow_null=True)
