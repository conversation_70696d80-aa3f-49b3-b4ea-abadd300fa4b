# Generated by Django 3.2 on 2025-08-13 08:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organizations", "0018_create_default_organization_roles"),
        ("workspaces", "0025_auto_20250813_0715"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="dataset",
            name="workspace",
        ),
        migrations.RemoveField(
            model_name="workspacerequest",
            name="workspace",
        ),
        migrations.AddField(
            model_name="dataset",
            name="organization",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="organizations",
                to="organizations.organization",
                verbose_name="Organization",
            ),
        ),
    ]
