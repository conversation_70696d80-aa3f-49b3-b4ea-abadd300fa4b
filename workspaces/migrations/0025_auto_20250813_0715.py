# Generated by Django 3.2 on 2025-08-13 07:15

import django.db.models.deletion
from django.db import migrations, models

import common.utils.models


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0024_alter_workspace_layers_sorted_ids"),
    ]

    operations = [
        migrations.AddField(
            model_name="workspacerequest",
            name="workspace",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="requests",
                to="workspaces.workspace",
                verbose_name="Workspace",
            ),
        ),
        migrations.AlterField(
            model_name="dataset",
            name="file",
            field=models.URLField(
                validators=[common.utils.models.dataset_extension_validator],
                verbose_name="Original Dataset File",
            ),
        ),
    ]
