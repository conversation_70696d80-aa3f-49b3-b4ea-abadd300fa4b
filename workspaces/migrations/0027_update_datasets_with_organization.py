# Generated by Django 3.2 on 2025-08-13 08:56

from django.db import migrations


def update_datasets_with_organization(apps, schema_editor):
    Dataset = apps.get_model("workspaces", "Dataset")
    datasets = Dataset.objects.filter(
        organization__isnull=True, layers__isnull=False
    ).prefetch_related("layers__workspace__organization")
    for dataset in datasets:
        dataset_layer = dataset.layers.last()
        dataset.organization = dataset_layer.workspace.organization
    Dataset.objects.bulk_update(datasets, ["organization"])


class Migration(migrations.Migration):

    dependencies = [
        ("workspaces", "0026_auto_20250813_0835"),
    ]

    operations = [
        migrations.RunPython(
            update_datasets_with_organization, migrations.RunPython.noop
        )
    ]
