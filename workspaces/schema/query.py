import graphene
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from gabbro.graphene.exceptions import BadRequest, NotFound

from common.handlers.dataset_files import DatasetFilesLoader
from common.utils import (
    PageInfo,
    DjangoFilterInput,
    authentication_required,
    filter_qs_paginate_with_count,
    build_q,
    organization_required,
    authorize_user,
    authorize_multiple_objects_for_user,
)
from organizations.models import Organization
from organizations.perms_constants import VIEW_WORKSPACE, ADD_WORKSPACE
from users.models import User
from workspaces.models import (
    Workspace,
    WorkspaceRequest,
    WorkspaceRequestChoices,
    Dataset,
    EDAReport,
)
from workspaces.schema.object_types import (
    WorkspaceListType,
    WorkspaceRequestListType,
    JsonSchemasType,
    PreviewDataListType,
    DatasetListType,
    EDAReportListType,
)
from workspaces.schema.utils import (
    get_predicted_jsonschema_from_dataset,
    mapping_sample_data_with_json_schem_title,
    get_geometry_columns_from_location_field_mapping,
)


class Query(graphene.ObjectType):
    workspaces = graphene.Field(
        WorkspaceListType,
        org_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    datasets = graphene.Field(
        DatasetListType,
        org_id=graphene.Int(required=True),
        pk=graphene.Int(),
        workspace_id=graphene.Int(required=True),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    workspace_requests = graphene.Field(
        WorkspaceRequestListType,
        org_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )
    dataset_sample_data = graphene.Field(
        PreviewDataListType,
        org_id=graphene.Int(required=True),
        dataset_request_id=graphene.Int(required=True),
    )
    json_schemas = graphene.Field(
        JsonSchemasType,
        org_id=graphene.Int(required=True),
        dataset_request_id=graphene.Int(required=True),
    )
    eda_reports = graphene.Field(
        EDAReportListType,
        org_id=graphene.Int(required=True),
        workspace_id=graphene.Int(required=True),
        layer_id=graphene.Int(required=True),
        pk=graphene.Int(),
        page_info=PageInfo(),
        filters=graphene.List(DjangoFilterInput),
    )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_workspaces(
        root, info, org_id: int, pk=None, page_info=None, filters=None, **kwargs
    ):
        user: User = info.context.user
        organization = info.context.organization
        if pk:
            queryset = retrieve_workspace(pk, user, organization)
        else:
            queryset = list_workspaces(user, organization)
        return WorkspaceListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_workspace_requests(
        root, info, org_id: int, pk=None, page_info=None, filters=None, **kwargs
    ):
        user: User = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=VIEW_WORKSPACE)

        workspace_requests = WorkspaceRequest.objects.permitted_objects(
            user=user,
            organization=organization,
            **{"status": WorkspaceRequestChoices.IN_PROGRESS},
        )

        return WorkspaceRequestListType(
            *filter_qs_paginate_with_count(
                workspace_requests, build_q(pk, filters), page_info
            )
        )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_dataset_sample_data(
        root, info, org_id: int, dataset_request_id: int, **kwargs
    ):
        user: User = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=ADD_WORKSPACE)

        dataset_request: WorkspaceRequest = (
            WorkspaceRequest.objects.permitted_objects(
                user=user,
                organization=organization,
                **{
                    "id": dataset_request_id,
                    "status": WorkspaceRequestChoices.IN_PROGRESS,
                },
            )
            .select_related("dataset")
            .first()
        )
        if not dataset_request:
            raise NotFound(
                reason={
                    "dataset_request_id": _("Invalid dataset_request_id: %(id)s")
                    % {"id": dataset_request_id}
                }
            )

        # Loading data from dataset file
        json_schema = dataset_request.layer_data.get("json_schema")
        sample_data = dataset_request.dataset.meta_data.get("sample_data")
        if not sample_data:
            loader = DatasetFilesLoader(file=dataset_request.dataset.file)
            sample_data = loader.get_sample_data(
                rows_number=settings.DATASET_SAMPLE_ROWS_NUMBER
            )
        if json_schema:
            sample_data = mapping_sample_data_with_json_schem_title(
                sample_data, json_schema
            )
            dataset_request.dataset.set_metadata({"sample_data": sample_data})
        return PreviewDataListType(data=sample_data)

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_json_schemas(
        root, info, org_id: int, dataset_request_id: int, **kwargs
    ):
        user: User = info.context.user
        organization = info.context.organization
        authorize_user(model_obj=organization, user=user, permission=ADD_WORKSPACE)

        dataset_request: WorkspaceRequest = (
            WorkspaceRequest.objects.permitted_objects(
                user=user,
                organization=organization,
                **{
                    "id": dataset_request_id,
                    "status": WorkspaceRequestChoices.IN_PROGRESS,
                    "layer_data__location_field_mapping__isnull": False,
                },
            )
            .select_related("dataset")
            .first()
        )
        if not dataset_request:
            raise BadRequest(
                reason={
                    "dataset_request_id": _("Invalid dataset_request_id: %(id)s")
                    % {"id": dataset_request_id}
                }
            )

        geometry_columns = get_geometry_columns_from_location_field_mapping(
            dataset_request.layer_data["location_field_mapping"]
        )
        data, json_schema = get_predicted_jsonschema_from_dataset(
            geometry_columns=geometry_columns, file=dataset_request.dataset.file
        )
        return JsonSchemasType(
            form_data=data[: settings.DATASET_SAMPLE_ROWS_NUMBER],
            json_schema=json_schema,
        )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_datasets(
        root,
        info,
        workspace_id: int,
        org_id: int,
        pk=None,
        page_info=None,
        filters=None,
        **kwargs,
    ):
        user = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise NotFound(
                reason={
                    "workspace_id": _("Workspace with id %(workspace_id)s not found")
                    % {"workspace_id": workspace_id}
                }
            )
        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
        )
        queryset = Dataset.objects.filter(layers__workspace=workspace).distinct()
        return DatasetListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )

    @staticmethod
    @authentication_required
    @organization_required
    def resolve_eda_reports(
        root,
        info,
        org_id: int,
        workspace_id: int,
        layer_id: int,
        pk=None,
        page_info=None,
        filters=None,
        **kwargs,
    ):
        user: User = info.context.user
        organization = info.context.organization
        workspace: Workspace = Workspace.objects.filter(
            organization=organization, id=workspace_id
        ).first()
        if not workspace:
            raise BadRequest(reason={"workspace_id": _("Invalid workspace_id") % {}})

        authorize_multiple_objects_for_user(
            models_objs=[organization, workspace], perm=VIEW_WORKSPACE, user=user
        )

        layer = workspace.layers.filter(id=layer_id).first()
        if not layer:
            raise NotFound(
                reason={
                    "layer_id": _("Layer with id %(layer_id)s not found")
                    % {"layer_id": layer_id}
                }
            )

        if layer.dataset is None:
            queryset = EDAReport.objects.none()
        else:
            queryset = layer.dataset.eda_reports.all()
        return EDAReportListType(
            *filter_qs_paginate_with_count(queryset, build_q(pk, filters), page_info)
        )


def list_workspaces(user: User, organization: Organization):
    authorize_user(model_obj=organization, user=user, permission=VIEW_WORKSPACE)
    workspaces = Workspace.objects.filter(organization=organization)
    if not workspaces:
        Workspace.create_default_workspace(owner=user, organization=organization)
        workspaces = Workspace.objects.filter(organization=organization, owner=user)
    return workspaces


def retrieve_workspace(pk: int, user: User, organization: Organization):
    workspaces = Workspace.objects.filter(pk=pk, organization=organization)
    if not workspaces:
        raise NotFound(
            reason={
                "workspace_id": _("Workspace with id %(workspace_id)s not found")
                % {"workspace_id": pk}
            }
        )
    authorize_multiple_objects_for_user(
        models_objs=[organization, workspaces[0]], perm=VIEW_WORKSPACE, user=user
    )
    return workspaces
