from unittest.mock import MagicMock

from common.tests.factories import RoleFactory, UserFactory
from common.tests.schema.query import BaseQueryTestCase


class PermissionsQueryTestCase(BaseQueryTestCase):
    maxDiff = None

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Add VIEW_ROLE permission to the role so tests can access permissions query
        cls.role.permissions.add(cls.perms["view_role"])

    def setUp(self):
        super().setUp()
        self.query = """
        query Permissions($orgId: Int!) {
            permissions(orgId: $orgId) {
                model
                permissions {
                    id
                    name
                    codename
                }
            }
        }
        """

    def test_permissions_query_success(self):
        """Test successful permissions query"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)
        self.assertIn("data", result)
        self.assertIn("permissions", result["data"])
        self.assertIsInstance(result["data"]["permissions"], list)

        # Should have grouped permissions by model
        self.assertGreater(len(result["data"]["permissions"]), 0)

        # Check structure of grouped permissions
        for permission_group in result["data"]["permissions"]:
            self.assertIn("model", permission_group)
            self.assertIn("permissions", permission_group)
            self.assertIsInstance(permission_group["permissions"], list)

            # Check individual permissions structure
            for permission in permission_group["permissions"]:
                self.assertIn("id", permission)
                self.assertIn("name", permission)
                self.assertIn("codename", permission)

    def test_permissions_query_unauthenticated(self):
        """Test permissions query with unauthenticated user"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.non_auth_request,
        )
        # Check that there are errors and it's an authentication error
        self.assertIn("errors", result)
        self._check_none_auth_error(result)

    def test_permissions_query_without_permission(self):
        """Test permissions query without VIEW_ROLE permission"""
        # Create a user without VIEW_ROLE permission
        role_without_permission = RoleFactory.create(
            organization=self.organization, permissions=[]
        )
        user_without_permission = UserFactory.create(
            organization=self.organization, roles=[role_without_permission]
        )

        auth_request_no_perm = MagicMock()
        auth_request_no_perm.user = user_without_permission

        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=auth_request_no_perm,
        )
        self.assertEquals(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 403,
                    "status_text": "Forbidden",
                    "reason": {"user": "Permission denied"},
                }
            },
        )

    def test_permissions_query_groups_by_model(self):
        """Test that permissions are correctly grouped by model"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Extract models from the result
        models = [group["model"] for group in result["data"]["permissions"]]

        # Should have user, workspace, and role models
        expected_models = ["user", "workspace", "role"]
        for expected_model in expected_models:
            self.assertIn(expected_model, models)

    def test_permissions_query_includes_user_permissions(self):
        """Test that user-related permissions are included"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Find user permissions group
        user_permissions_group = None
        for group in result["data"]["permissions"]:
            if group["model"] == "user":
                user_permissions_group = group
                break

        self.assertIsNotNone(user_permissions_group)

        # Check for expected user permissions
        permission_codenames = [
            perm["codename"] for perm in user_permissions_group["permissions"]
        ]
        expected_user_permissions = [
            "view_user",
            "add_user",
            "change_user",
            "delete_user",
        ]
        for expected_perm in expected_user_permissions:
            self.assertIn(expected_perm, permission_codenames)

    def test_permissions_query_includes_workspace_permissions(self):
        """Test that workspace-related permissions are included"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Find workspace permissions group
        workspace_permissions_group = None
        for group in result["data"]["permissions"]:
            if group["model"] == "workspace":
                workspace_permissions_group = group
                break

        self.assertIsNotNone(workspace_permissions_group)

        # Check for expected workspace permissions
        permission_codenames = [
            perm["codename"] for perm in workspace_permissions_group["permissions"]
        ]
        expected_workspace_permissions = [
            "view_workspace",
            "add_workspace",
            "change_workspace",
            "delete_workspace",
        ]
        for expected_perm in expected_workspace_permissions:
            self.assertIn(expected_perm, permission_codenames)

    def test_permissions_query_includes_role_permissions(self):
        """Test that role-related permissions are included"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Find role permissions group
        role_permissions_group = None
        for group in result["data"]["permissions"]:
            if group["model"] == "role":
                role_permissions_group = group
                break

        self.assertIsNotNone(role_permissions_group)

        # Check for expected role permissions
        permission_codenames = [
            perm["codename"] for perm in role_permissions_group["permissions"]
        ]
        expected_role_permissions = [
            "view_role",
            "add_role",
            "change_role",
            "delete_role",
        ]
        for expected_perm in expected_role_permissions:
            self.assertIn(expected_perm, permission_codenames)

    def test_permissions_query_filters_organization_content_type(self):
        """Test that only organization content type permissions are returned"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # All permissions should be related to organization model
        for group in result["data"]["permissions"]:
            for permission in group["permissions"]:
                # The permission codename should end with _user, _workspace, or _role
                codename = permission["codename"]
                self.assertTrue(
                    codename.endswith("_user")
                    or codename.endswith("_workspace")
                    or codename.endswith("_role"),
                    f"Permission {codename} doesn't match expected pattern",
                )

    def test_permissions_query_invalid_organization(self):
        """Test permissions query with invalid organization ID"""
        variables = {
            "orgId": 99999,  # Non-existent organization
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertIn("errors", result)
        self.assertEquals(
            result["errors"][0]["extensions"],
            {
                "http": {
                    "status": 404,
                    "status_text": "Not Found",
                    "reason": {"orgId": "Organization with id 99999 not found"},
                }
            },
        )

    def test_permissions_query_consistent_structure(self):
        """Test that permissions query returns consistent structure"""
        variables = {
            "orgId": self.organization.id,
        }
        result = self.client.execute(
            self.query,
            variables=variables,
            context=self.auth_request,
        )
        self.assertNotIn("errors", result)

        # Verify each group has the required structure
        for group in result["data"]["permissions"]:
            self.assertIsInstance(group["model"], str)
            self.assertIsInstance(group["permissions"], list)

            # Verify each permission has required fields
            for permission in group["permissions"]:
                self.assertIsInstance(
                    permission["id"], str
                )  # GraphQL returns string IDs
                self.assertIsInstance(permission["name"], str)
                self.assertIsInstance(permission["codename"], str)

                # Verify permission name and codename are not empty
                self.assertGreater(len(permission["name"]), 0)
                self.assertGreater(len(permission["codename"]), 0)
